# 应用配置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Mystical Website"

# 数据库配置
# Supabase PostgreSQL - 你的项目配置
# 请将下面的 [YOUR_PASSWORD] 替换为你从 Supabase 复制的完整连接字符串
DATABASE_URL="postgresql://postgres:[YOUR_PASSWORD]@db.sedxknrtipqqomjcsxad.supabase.co:5432/postgres?schema=public"

# 本地开发数据库 (可选)
# DATABASE_URL="postgresql://username:password@localhost:5432/mystical_dev"

# Prisma 相关配置
DIRECT_URL="postgresql://postgres:[YOUR_PASSWORD]@db.sedxknrtipqqomjcsxad.supabase.co:5432/postgres?schema=public"

# Redis缓存
REDIS_URL="redis://localhost:6379"
UPSTASH_REDIS_REST_URL=""
UPSTASH_REDIS_REST_TOKEN=""

# 认证配置
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# AI服务配置
# 通义千问 (主要AI服务)
QWEN_API_KEY="your-qwen-api-key"
QWEN_API_URL="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# 豆包 (备用AI服务)
DOUBAO_API_KEY="your-doubao-api-key"
DOUBAO_API_URL="https://ark.cn-beijing.volces.com/api/v3/chat/completions"

# 智谱AI (多语言支持)
ZHIPU_API_KEY="your-zhipu-api-key"
ZHIPU_API_URL="https://open.bigmodel.cn/api/paas/v4/chat/completions"

# 内容管理
SANITY_PROJECT_ID="your-sanity-project-id"
SANITY_DATASET="production"
SANITY_API_TOKEN="your-sanity-token"

# 监控和分析
SENTRY_DSN="your-sentry-dsn"
SENTRY_ORG="your-sentry-org"
SENTRY_PROJECT="your-sentry-project"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"

# Umami分析
NEXT_PUBLIC_UMAMI_WEBSITE_ID="your-umami-website-id"
NEXT_PUBLIC_UMAMI_URL="https://umami.mystical-website.com"

# 邮件服务
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# 文件存储 - Supabase 配置
NEXT_PUBLIC_SUPABASE_URL="https://sedxknrtipqqomjcsxad.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNlZHhrbnJ0aXBxcW9tamNzeGFkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NDMzNDIsImV4cCI6MjA2OTUxOTM0Mn0.8XZW0ZYe7tIy9io19Zba-oqSbrq03o3kmb9TVf_ZwVY"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# 社交媒体集成
NEXT_PUBLIC_FACEBOOK_APP_ID="your-facebook-app-id"
NEXT_PUBLIC_TWITTER_HANDLE="@mystical_website"
NEXT_PUBLIC_INSTAGRAM_HANDLE="mystical_website"

# 搜索引擎优化
NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION="your-google-site-verification"
NEXT_PUBLIC_BING_SITE_VERIFICATION="your-bing-site-verification"

# 开发工具
ANALYZE=false
SKIP_ENV_VALIDATION=false

# Vercel部署 (在Vercel Dashboard中配置)
# VERCEL_TOKEN="your-vercel-token"
# VERCEL_ORG_ID="your-vercel-org-id"
# VERCEL_PROJECT_ID="your-vercel-project-id"
